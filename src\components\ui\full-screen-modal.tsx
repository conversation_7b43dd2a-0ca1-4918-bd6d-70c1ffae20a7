'use client'

import * as React from 'react'
import * as DialogPrimitive from '@radix-ui/react-dialog'
import { X } from 'lucide-react'

import { cn } from '@/lib/utils'

const FullScreenModal = DialogPrimitive.Root

const FullScreenModalTrigger = DialogPrimitive.Trigger

const FullScreenModalPortal = DialogPrimitive.Portal

const FullScreenModalClose = DialogPrimitive.Close

const FullScreenModalOverlay = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Overlay
    ref={ref}
    className={cn(
      'fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0',
      className,
    )}
    {...props}
  />
))
FullScreenModalOverlay.displayName = DialogPrimitive.Overlay.displayName

const FullScreenModalContent = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <FullScreenModalPortal>
    <FullScreenModalOverlay />
    <DialogPrimitive.Content
      ref={ref}
      className={cn(
        'fixed inset-0 z-50 flex h-full w-full flex-col bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95',
        className,
      )}
      {...props}
    >
      {children}
      <DialogPrimitive.Close className="absolute right-6 top-6 rounded-full bg-primary/10 p-2 text-primary opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none">
        <X className="h-5 w-5" />
        <span className="sr-only">Close</span>
      </DialogPrimitive.Close>
    </DialogPrimitive.Content>
  </FullScreenModalPortal>
))
FullScreenModalContent.displayName = DialogPrimitive.Content.displayName

const FullScreenModalHeader = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn('flex flex-col space-y-1.5 px-6 pt-6 text-center sm:text-left', className)}
    {...props}
  />
)
FullScreenModalHeader.displayName = 'FullScreenModalHeader'

const FullScreenModalFooter = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      'flex flex-col-reverse px-6 pb-6 sm:flex-row sm:justify-end sm:space-x-2',
      className,
    )}
    {...props}
  />
)
FullScreenModalFooter.displayName = 'FullScreenModalFooter'

const FullScreenModalTitle = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Title
    ref={ref}
    className={cn('text-2xl font-semibold leading-none tracking-tight', className)}
    {...props}
  />
))
FullScreenModalTitle.displayName = DialogPrimitive.Title.displayName

const FullScreenModalDescription = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Description>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Description
    ref={ref}
    className={cn('text-sm text-muted-foreground', className)}
    {...props}
  />
))
FullScreenModalDescription.displayName = DialogPrimitive.Description.displayName

export {
  FullScreenModal,
  FullScreenModalPortal,
  FullScreenModalOverlay,
  FullScreenModalClose,
  FullScreenModalContent,
  FullScreenModalHeader,
  FullScreenModalFooter,
  FullScreenModalTitle,
  FullScreenModalDescription,
}
