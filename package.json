{"name": "coop<PERSON><PERSON>ist", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "format": "prettier -c \"**/*.{css,js,json,jsx,ts,tsx}\"", "format:fix": "prettier -c \"**/*.{css,js,json,jsx,ts,tsx}\" --write", "format:fix:src": "prettier -c './src/**/*.{ts,tsx,js}' --write", "lint": "next lint --fix --dir app --dir src", "prepare": "husky || true", "test": "jest", "lint-staged": "lint-staged", "commitlint": "commitlint"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}, "dependencies": {"@emotion/is-prop-valid": "latest", "@hookform/resolvers": "latest", "@radix-ui/react-accordion": "latest", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "latest", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "latest", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "latest", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "latest", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "latest", "@radix-ui/react-toast": "latest", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "latest", "@reduxjs/toolkit": "latest", "@sendgrid/mail": "^8.1.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "crypto": "latest", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "framer-motion": "latest", "fs": "latest", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "next": "15.1.0", "next-themes": "latest", "nodemailer": "^6.10.0", "path": "latest", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-hook-form": "latest", "react-redux": "latest", "react-resizable-panels": "^2.1.7", "recharts": "latest", "redux": "latest", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "latest"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.3", "husky": "^9.1.7", "lint-staged": "^15.5.1", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}