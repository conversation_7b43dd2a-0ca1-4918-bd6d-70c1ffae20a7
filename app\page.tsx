import type { Metada<PERSON> } from 'next'
import { <PERSON><PERSON> } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { HeroSection } from '@/components/home/<USER>'
import { PurposeSection } from '@/components/home/<USER>'
import { WaitlistSection } from '@/components/home/<USER>'
import { BenefitsSection } from '@/components/home/<USER>'
import { siteConfig } from '@/data/site-config'

export const metadata: Metadata = {
  title: siteConfig.name,
  description: siteConfig.description,
  keywords: siteConfig.keywords,
  themeColor: siteConfig.themeColor,
}

export default function Home() {
  return (
    <main className="flex min-h-screen flex-col">
      {/* Header */}
      <Header title={siteConfig.name} />

      {/* Hero Section */}
      <HeroSection />

      {/* Content Sections */}
      <PurposeSection />
      <BenefitsSection />
      <WaitlistSection />

      {/* Footer */}
      <Footer />
    </main>
  )
}
